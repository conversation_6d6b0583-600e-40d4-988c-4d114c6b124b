@tailwind base;
@tailwind components;
@tailwind utilities;

/* Premium Finance Theme - All colors MUST be HSL */

@layer base {
  :root {
    --background: 210 20% 98%;
    --foreground: 213 27% 14%;

    --card: 0 0% 100%;
    --card-foreground: 213 27% 14%;

    --popover: 0 0% 100%;
    --popover-foreground: 213 27% 14%;

    --primary: 213 94% 68%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 213 94% 78%;

    --secondary: 147 50% 47%;
    --secondary-foreground: 0 0% 100%;

    --success: 147 69% 58%;
    --success-foreground: 0 0% 100%;

    --warning: 35 91% 62%;
    --warning-foreground: 0 0% 100%;

    --muted: 210 20% 96%;
    --muted-foreground: 213 20% 55%;

    --accent: 213 94% 95%;
    --accent-foreground: 213 94% 68%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 210 20% 90%;
    --input: 210 20% 94%;
    --ring: 213 94% 68%;

    --radius: 0.75rem;

    /* Premium gradients */
    --gradient-primary: linear-gradient(135deg, hsl(213 94% 68%), hsl(213 94% 78%));
    --gradient-success: linear-gradient(135deg, hsl(147 50% 47%), hsl(147 69% 58%));
    --gradient-card: linear-gradient(135deg, hsl(0 0% 100%), hsl(210 20% 98%));
    --gradient-hero: linear-gradient(135deg, hsl(213 94% 68%) 0%, hsl(147 50% 47%) 100%);
    
    /* Professional shadows */
    --shadow-card: 0 4px 6px -1px hsl(213 27% 14% / 0.1), 0 2px 4px -2px hsl(213 27% 14% / 0.1);
    --shadow-elevated: 0 10px 15px -3px hsl(213 27% 14% / 0.1), 0 4px 6px -4px hsl(213 27% 14% / 0.1);
    --shadow-premium: 0 20px 25px -5px hsl(213 27% 14% / 0.1), 0 8px 10px -6px hsl(213 27% 14% / 0.1);

    /* Finance-specific colors */
    --profit: 147 69% 58%;
    --loss: 0 84% 60%;
    --pending: 35 91% 62%;
    --paid: 147 50% 47%;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}